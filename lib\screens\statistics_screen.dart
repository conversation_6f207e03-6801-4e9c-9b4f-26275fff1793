import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../database/database_helper.dart';
import '../utils/number_formatter.dart';
import '../providers/card_type_provider.dart';
import '../providers/card_inventory_provider.dart';
import '../providers/debt_provider.dart';
import '../models/custom_card_type.dart';
import '../models/card_inventory.dart';
import '../models/debt.dart';
import 'daily_sales_debts_screen.dart';

class StatisticsScreen extends StatefulWidget {
  const StatisticsScreen({super.key});

  @override
  State<StatisticsScreen> createState() => _StatisticsScreenState();
}

class _StatisticsScreenState extends State<StatisticsScreen>
    with TickerProviderStateMixin {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  // التنقل إلى ديون اليوم
  void _navigateToTodayDebts(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            const DailySalesDebtsScreen(isToday: true, title: 'ديون اليوم'),
      ),
    );
  }

  // التنقل إلى ديون الأمس
  void _navigateToYesterdayDebts(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            const DailySalesDebtsScreen(isToday: false, title: 'ديون الأمس'),
      ),
    );
  }

  // جلب إحصائيات اليوم والأمس (المبيعات فقط - ديون العملاء لك)
  Future<Map<String, dynamic>> _getDailySalesStats() async {
    final allDebts = await _databaseHelper.getAllDebts();
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));

    // فلترة المبيعات فقط (ديون العملاء لك وليس عليك)
    final todayDebts = allDebts.where((debt) {
      final debtDay = DateTime(
        debt.entryDate.year,
        debt.entryDate.month,
        debt.entryDate.day,
      );
      return debtDay.isAtSameMomentAs(today) &&
          debt.direction == DebtDirection.customerOwesMe;
    }).toList();

    final yesterdayDebts = allDebts.where((debt) {
      final debtDay = DateTime(
        debt.entryDate.year,
        debt.entryDate.month,
        debt.entryDate.day,
      );
      return debtDay.isAtSameMomentAs(yesterday) &&
          debt.direction == DebtDirection.customerOwesMe;
    }).toList();

    return {
      'today': {
        'count': todayDebts.length,
        'amount': todayDebts.fold<double>(0, (sum, debt) => sum + debt.amount),
      },
      'yesterday': {
        'count': yesterdayDebts.length,
        'amount': yesterdayDebts.fold<double>(
          0,
          (sum, debt) => sum + debt.amount,
        ),
      },
    };
  }

  // جلب إحصائيات الأسبوع (المبيعات فقط)
  Future<Map<String, dynamic>> _getWeeklySalesStats() async {
    final allDebts = await _databaseHelper.getAllDebts();
    final now = DateTime.now();
    final weekStart = now.subtract(Duration(days: now.weekday - 1));
    final weekStartDay = DateTime(
      weekStart.year,
      weekStart.month,
      weekStart.day,
    );

    // فلترة المبيعات فقط (ديون العملاء لك)
    final weekDebts = allDebts.where((debt) {
      return debt.entryDate.isAfter(
            weekStartDay.subtract(const Duration(days: 1)),
          ) &&
          debt.entryDate.isBefore(now.add(const Duration(days: 1))) &&
          debt.direction == DebtDirection.customerOwesMe;
    }).toList();

    return {
      'count': weekDebts.length,
      'amount': weekDebts.fold<double>(0, (sum, debt) => sum + debt.amount),
    };
  }

  // جلب إحصائيات الشهر (المبيعات فقط)
  Future<Map<String, dynamic>> _getMonthlySalesStats() async {
    final allDebts = await _databaseHelper.getAllDebts();
    final now = DateTime.now();
    final monthStart = DateTime(now.year, now.month);

    // فلترة المبيعات فقط (ديون العملاء لك)
    final monthDebts = allDebts.where((debt) {
      return debt.entryDate.isAfter(
            monthStart.subtract(const Duration(days: 1)),
          ) &&
          debt.entryDate.isBefore(now.add(const Duration(days: 1))) &&
          debt.direction == DebtDirection.customerOwesMe;
    }).toList();

    return {
      'count': monthDebts.length,
      'amount': monthDebts.fold<double>(0, (sum, debt) => sum + debt.amount),
    };
  }

  // جلب إحصائيات المبيعات حسب نوع الكارت (المبيعات فقط)
  Future<List<Map<String, dynamic>>> _getSalesByCardTypeFiltered() async {
    final allDebts = await _databaseHelper.getAllDebts();

    // فلترة المبيعات فقط (ديون العملاء لك)
    final salesDebts = allDebts.where((debt) {
      return debt.direction == DebtDirection.customerOwesMe;
    }).toList();

    // تجميع البيانات حسب نوع الكارت
    final Map<String, Map<String, dynamic>> groupedData = {};

    for (final debt in salesDebts) {
      final cardType = debt.cardType;

      if (groupedData.containsKey(cardType)) {
        groupedData[cardType]!['count'] =
            (groupedData[cardType]!['count'] as int) + 1;
        groupedData[cardType]!['total_quantity'] =
            (groupedData[cardType]!['total_quantity'] as int) +
                debt.remainingQuantity;
        groupedData[cardType]!['total_amount'] =
            (groupedData[cardType]!['total_amount'] as double) + debt.amount;
      } else {
        groupedData[cardType] = {
          'card_type': cardType,
          'count': 1,
          'total_quantity': debt.remainingQuantity,
          'total_amount': debt.amount,
          'avg_amount': debt.amount,
        };
      }
    }

    // حساب المتوسط وتحويل إلى قائمة
    final result = groupedData.values.map((data) {
      data['avg_amount'] =
          (data['total_amount'] as double) / (data['count'] as int);
      return data;
    }).toList();

    // ترتيب حسب إجمالي المبلغ
    result.sort(
      (a, b) =>
          (b['total_amount'] as double).compareTo(a['total_amount'] as double),
    );

    return result;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(110),
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Colors.deepPurple.shade600,
                Colors.indigo.shade600,
                Colors.blue.shade600,
                Colors.cyan.shade500,
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              stops: const [0.0, 0.3, 0.7, 1.0],
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.deepPurple.withValues(alpha: 0.3),
                blurRadius: 15,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: AppBar(
            title: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.analytics,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                const Text(
                  'الإحصائيات المتكاملة',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    fontSize: 18,
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.transparent,
            elevation: 0,
            iconTheme: const IconThemeData(color: Colors.white),
            bottom: PreferredSize(
              preferredSize: const Size.fromHeight(60),
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.2),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: TabBar(
                  controller: _tabController,
                  indicator: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  indicatorSize: TabBarIndicatorSize.tab,
                  labelColor: Colors.grey.shade800,
                  unselectedLabelColor: Colors.white.withValues(alpha: 0.9),
                  labelStyle: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                  unselectedLabelStyle: const TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 13,
                  ),
                  dividerColor: Colors.transparent,
                  tabs: const [
                    Tab(
                      height: 44,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.analytics_outlined, size: 20),
                          SizedBox(width: 8),
                          Text('عامة'),
                        ],
                      ),
                    ),
                    Tab(
                      height: 44,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.trending_up_outlined, size: 20),
                          SizedBox(width: 8),
                          Text('المبيعات'),
                        ],
                      ),
                    ),
                    Tab(
                      height: 44,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.inventory_2_outlined, size: 20),
                          SizedBox(width: 8),
                          Text('المخزون'),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildGeneralStatistics(),
          _buildSalesStatistics(),
          _buildStockStatistics(),
        ],
      ),
    );
  }

  // الإحصائيات العامة
  Widget _buildGeneralStatistics() {
    return FutureBuilder<Map<String, dynamic>>(
      future: _databaseHelper.getStatistics(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return _buildErrorWidget();
        }

        final statistics = snapshot.data ?? {};
        return ListView(
          padding: const EdgeInsets.all(16),
          children: [
            _buildOverviewCards(statistics),
            const SizedBox(height: 24),
            _buildYearlySalesChart(),
          ],
        );
      },
    );
  }

  // إحصائيات المبيعات
  Widget _buildSalesStatistics() {
    return FutureBuilder<List<dynamic>>(
      future: Future.wait([
        _getSalesByCardTypeFiltered(),
        _getDailySalesStats(),
        _getWeeklySalesStats(),
        _getMonthlySalesStats(),
      ]),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return _buildErrorWidget();
        }

        final results = snapshot.data ?? [];
        final salesData = results[0] as List<Map<String, dynamic>>? ?? [];
        final todayStats = results[1] as Map<String, dynamic>? ?? {};
        final weekStats = results[2] as Map<String, dynamic>? ?? {};
        final monthStats = results[3] as Map<String, dynamic>? ?? {};

        return ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // 1. تحليل النشاط اليومي (أول عنصر)
            _buildTodayAnalyticsChart(todayStats['today'] ?? {}),
            const SizedBox(height: 40),

            // 2. ملخص المبيعات
            _buildSalesOverview(salesData),
            const SizedBox(height: 20),

            // 3. مبيعات اليوم والأمس (بدون تحليل النشاط)
            _buildDailySalesCardsOnly(todayStats),
            const SizedBox(height: 16),

            // 4. مبيعات الأسبوع والشهر
            _buildPeriodSalesCards(weekStats, monthStats),
            const SizedBox(height: 20),

            // 5. الكارتات المتأخرة
            _buildCardTypeSalesAnalysis(salesData),
            const SizedBox(height: 24),

            // 6. إحصائيات السداد
            _buildPaymentStatistics(),
          ],
        );
      },
    );
  }

  // إحصائيات المخزون
  Widget _buildStockStatistics() {
    return FutureBuilder<Map<String, dynamic>>(
      future: _databaseHelper.getStockStatistics(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return _buildErrorWidget();
        }

        final stockData = snapshot.data ?? <String, dynamic>{};

        return Consumer2<CardInventoryProvider, CardTypeProvider>(
          builder: (context, inventoryProvider, cardTypeProvider, child) {
            // تحديث البيانات عند دخول التبويب
            if (!inventoryProvider.isLoading &&
                inventoryProvider.inventories.isEmpty) {
              Future.microtask(() => inventoryProvider.loadInventories());
            }

            return RefreshIndicator(
              onRefresh: () async {
                await inventoryProvider.loadInventories();
                await cardTypeProvider.loadCustomCardTypes();
              },
              child: ListView(
                padding: const EdgeInsets.all(16),
                children: [
                  // إحصائيات المخزون التقليدية
                  _buildStockOverview(stockData),
                  const SizedBox(height: 16),

                  // توزيع المخزون مباشرة تحت الملخص
                  if (inventoryProvider.isLoading)
                    const Center(
                      child: Padding(
                        padding: EdgeInsets.all(32.0),
                        child: CircularProgressIndicator(),
                      ),
                    )
                  else if (inventoryProvider.inventories.isNotEmpty)
                    _buildInventoryDistributionChart(
                      inventoryProvider,
                      cardTypeProvider,
                    )
                  else
                    _buildEmptyStockState(),
                ],
              ),
            );
          },
        );
      },
    );
  }

  // حالة المخزون الفارغ
  Widget _buildEmptyStockState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inventory_2_outlined,
            size: 80,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد بطاقات في المخزون',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'اذهب إلى شاشة المخزون لإضافة بطاقات',
            style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
          ),
        ],
      ),
    );
  }

  // ويدجت الخطأ
  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error, size: 64, color: Colors.red.shade400),
          const SizedBox(height: 16),
          Text(
            'حدث خطأ في تحميل البيانات',
            style: TextStyle(fontSize: 18, color: Colors.red.shade600),
          ),
          const SizedBox(height: 8),
          Text(
            'يرجى المحاولة مرة أخرى',
            style: TextStyle(color: Colors.grey.shade600),
          ),
        ],
      ),
    );
  }

  // بطاقات المبيعات اليومية (بدون تحليل النشاط)
  Widget _buildDailySalesCardsOnly(Map<String, dynamic> todayStats) {
    final todayData = todayStats['today'] ?? {};
    final yesterdayData = todayStats['yesterday'] ?? {};

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان مبيعات اليوم والأمس المحسن
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.blue.shade600, Colors.blue.shade400],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.blue.withValues(alpha: 0.3),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(Icons.today, color: Colors.white, size: 24),
              ),
              const SizedBox(width: 16),
              const Text(
                'مبيعات اليوم والأمس',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 12),
        // بطاقة اليوم
        GestureDetector(
          onTap: () => _navigateToTodayDebts(context),
          child: _buildEnhancedStatCard(
            'مبيعات اليوم',
            NumberFormatter.formatCurrency((todayData['amount'] ?? 0).toInt()),
            _build3DIcon(Icons.wb_sunny, Colors.orange, Colors.yellow),
            Colors.grey,
            todayData['count'] ?? 0,
            'كارت',
          ),
        ),

        const SizedBox(height: 16),

        // بطاقة الأمس
        GestureDetector(
          onTap: () => _navigateToYesterdayDebts(context),
          child: _buildEnhancedStatCard(
            'مبيعات الأمس',
            NumberFormatter.formatCurrency(
              (yesterdayData['amount'] ?? 0).toInt(),
            ),
            _build3DIcon(Icons.nightlight_round, Colors.indigo, Colors.blue),
            Colors.grey,
            yesterdayData['count'] ?? 0,
            'كارت',
          ),
        ),
      ],
    );
  }

  // بطاقات المبيعات الأسبوعية والشهرية
  Widget _buildPeriodSalesCards(
    Map<String, dynamic> weekStats,
    Map<String, dynamic> monthStats,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان مبيعات الأسبوع والشهر المحسن
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.purple.shade600, Colors.purple.shade400],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.purple.withValues(alpha: 0.3),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(
                  Icons.calendar_view_week,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              const Text(
                'مبيعات الأسبوع والشهر',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 12),
        // بطاقة الأسبوع - حجم كبير
        _buildEnhancedStatCard(
          'مبيعات الأسبوع',
          NumberFormatter.formatCurrency((weekStats['amount'] ?? 0).toInt()),
          _build3DIcon(Icons.date_range, Colors.orange, Colors.deepOrange),
          Colors.grey,
          weekStats['count'] ?? 0,
          'كارت',
        ),

        const SizedBox(height: 16),

        // بطاقة الشهر - حجم كبير
        _buildEnhancedStatCard(
          'مبيعات الشهر',
          NumberFormatter.formatCurrency((monthStats['amount'] ?? 0).toInt()),
          _build3DIcon(Icons.calendar_month, Colors.purple, Colors.deepPurple),
          Colors.grey,
          monthStats['count'] ?? 0,
          'كارت',
        ),
      ],
    );
  }

  // مخطط نشاط اليوم الشامل والمتكامل
  Widget _buildTodayAnalyticsChart(Map<String, dynamic> todayData) {
    return FutureBuilder<Map<String, dynamic>>(
      future: _getTodayPaymentStats(),
      builder: (context, snapshot) {
        final paymentData = snapshot.data ?? {};

        final salesAmount = (todayData['amount'] ?? 0).toDouble();
        final salesCount = todayData['count'] ?? 0;
        final paymentAmount = (paymentData['amount'] ?? 0).toDouble();
        final paymentCount = paymentData['count'] ?? 0;

        final totalAmount = salesAmount + paymentAmount;
        final salesPercentage =
            totalAmount > 0 ? (salesAmount / totalAmount * 100) : 0.0;
        final paymentPercentage =
            totalAmount > 0 ? (paymentAmount / totalAmount * 100) : 0.0;

        return Container(
          margin: const EdgeInsets.only(bottom: 20),
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.white,
                Colors.blue.shade50.withValues(alpha: 0.3),
              ],
            ),
            borderRadius: BorderRadius.circular(24),
            border: Border.all(color: Colors.blue.withValues(alpha: 0.1)),
            boxShadow: [
              BoxShadow(
                color: Colors.blue.withValues(alpha: 0.08),
                blurRadius: 20,
                offset: const Offset(0, 8),
                spreadRadius: 2,
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // العنوان الرئيسي
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [Colors.blue.shade600, Colors.blue.shade400],
                  ),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.blue.withValues(alpha: 0.3),
                      blurRadius: 12,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: const Icon(
                        Icons.analytics_outlined,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    const Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'تحليل النشاط اليومي',
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                          SizedBox(height: 4),
                          Text(
                            'مقارنة شاملة بين المبيعات والمدفوعات',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.white70,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 24),

              // الإحصائيات الرئيسية في صفوف
              Row(
                children: [
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: Colors.blue.withValues(alpha: 0.1),
                        ),
                        boxShadow: [
                          BoxShadow(color: Colors.blue.withValues(alpha: 0.05)),
                        ],
                      ),
                      child: Column(
                        children: [
                          // دائرة المبيعات
                          SizedBox(
                            width: 80,
                            height: 80,
                            child: Stack(
                              alignment: Alignment.center,
                              children: [
                                // الخلفية
                                Container(
                                  width: 80,
                                  height: 80,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: Colors.blue.shade50,
                                  ),
                                ),
                                // الدائرة التقدمية
                                SizedBox(
                                  width: 70,
                                  height: 70,
                                  child: CircularProgressIndicator(
                                    value: salesPercentage / 100,
                                    strokeWidth: 8,
                                    backgroundColor: Colors.blue.shade100,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.blue.shade600,
                                    ),
                                    strokeCap: StrokeCap.round,
                                  ),
                                ),
                                // النسبة في المنتصف
                                Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Text(
                                      '${salesPercentage.toInt()}%',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.blue.shade700,
                                      ),
                                    ),
                                    Text(
                                      'مبيعات',
                                      style: TextStyle(
                                        fontSize: 10,
                                        fontWeight: FontWeight.w600,
                                        color: Colors.blue.shade600,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 12),
                          // تفاصيل المبيعات
                          Text(
                            NumberFormatter.formatCurrency(salesAmount.toInt()),
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.blue.shade700,
                            ),
                          ),
                          Text(
                            '$salesCount كارت',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.blue.shade600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: Colors.green.withValues(alpha: 0.1),
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.green.withValues(alpha: 0.05),
                          ),
                        ],
                      ),
                      child: Column(
                        children: [
                          // دائرة المدفوعات
                          SizedBox(
                            width: 80,
                            height: 80,
                            child: Stack(
                              alignment: Alignment.center,
                              children: [
                                // الخلفية
                                Container(
                                  width: 80,
                                  height: 80,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: Colors.green.shade50,
                                  ),
                                ),
                                // الدائرة التقدمية
                                SizedBox(
                                  width: 70,
                                  height: 70,
                                  child: CircularProgressIndicator(
                                    value: paymentPercentage / 100,
                                    strokeWidth: 8,
                                    backgroundColor: Colors.green.shade100,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.green.shade600,
                                    ),
                                    strokeCap: StrokeCap.round,
                                  ),
                                ),
                                // النسبة في المنتصف
                                Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Text(
                                      '${paymentPercentage.toInt()}%',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.green.shade700,
                                      ),
                                    ),
                                    Text(
                                      'مدفوعات',
                                      style: TextStyle(
                                        fontSize: 10,
                                        fontWeight: FontWeight.w600,
                                        color: Colors.green.shade600,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 12),
                          // تفاصيل المدفوعات
                          Text(
                            NumberFormatter.formatCurrency(
                              paymentAmount.toInt(),
                            ),
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.green.shade700,
                            ),
                          ),
                          Text(
                            '$paymentCount كارت',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.green.shade600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 24),

              // مؤشر المقارنة والتفوق
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.grey.shade50,
                      Colors.grey.shade100.withValues(alpha: 0.5),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: Colors.grey.withValues(alpha: 0.2)),
                ),
                child: Column(
                  children: [
                    // عنوان المقارنة
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.compare_arrows,
                          color: Colors.grey.shade700,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'مقارنة الأداء',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.grey.shade800,
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // مؤشر التفوق
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: salesAmount > paymentAmount
                            ? Colors.blue.shade50
                            : Colors.green.shade50,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: salesAmount > paymentAmount
                              ? Colors.blue.withValues(alpha: 0.2)
                              : Colors.green.withValues(alpha: 0.2),
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            salesAmount > paymentAmount
                                ? Icons.trending_up
                                : Icons.trending_down,
                            size: 20,
                            color: salesAmount > paymentAmount
                                ? Colors.blue.shade600
                                : Colors.green.shade600,
                          ),
                          const SizedBox(width: 8),
                          Flexible(
                            child: Text(
                              salesAmount > paymentAmount
                                  ? 'المبيعات تتفوق بـ ${((salesAmount - paymentAmount) / totalAmount * 100).toStringAsFixed(0)}%'
                                  : 'التسديدات تتفوق بـ ${((paymentAmount - salesAmount) / totalAmount * 100).toStringAsFixed(0)}%',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: salesAmount > paymentAmount
                                    ? Colors.blue.shade700
                                    : Colors.green.shade700,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 12),

                    // الإجمالي
                    Text(
                      'الإجمالي: ${NumberFormatter.formatCurrency(totalAmount.toInt())}',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey.shade700,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // إنشاء أيقونة ثلاثية الأبعاد
  Widget _build3DIcon(IconData icon, Color primaryColor, Color secondaryColor) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [primaryColor, secondaryColor],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: primaryColor.withValues(alpha: 0.3),
            offset: const Offset(0, 4),
          ),
          BoxShadow(
            color: Colors.white.withValues(alpha: 0.8),
            blurRadius: 4,
            offset: const Offset(-2, -2),
          ),
        ],
      ),
      child: Icon(
        icon,
        color: Colors.white,
        size: 24,
        shadows: [
          Shadow(
            color: Colors.black.withValues(alpha: 0.3),
            offset: const Offset(1, 1),
            blurRadius: 2,
          ),
        ],
      ),
    );
  }

  // بطاقة إحصائية محسنة مع تفاصيل الكارتات
  Widget _buildEnhancedStatCardWithDetails(
    String title,
    String value,
    Widget iconWidget,
    Color borderColor,
    String cardDetails,
  ) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.2)),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              iconWidget,
              const Spacer(),
              // عرض تفاصيل الكارتات
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey.withValues(alpha: 0.2)),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.inventory_2_outlined,
                      size: 12,
                      color: Colors.grey.shade600,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      cardDetails,
                      style: TextStyle(
                        color: Colors.grey.shade700,
                        fontSize: 11,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.grey.shade700,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Text(
                'المبلغ: ',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey.shade600,
                ),
              ),
              Text(
                value,
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(width: 4),
              Text(
                'د.ع',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // تحديد النص المناسب لكل بطاقة
  String _getStatLabel(String title) {
    switch (title) {
      case 'إجمالي الكارتات':
        return 'العدد: ';
      case 'أنواع البطاقات':
        return 'العدد: ';
      case 'الرصيد المتبقي':
        return 'الكمية: ';
      case 'مخزون منخفض':
        return 'العدد: ';
      case 'مخزون نافد':
        return 'العدد: ';
      default:
        return 'المبلغ: ';
    }
  }

  // تحديد ما إذا كان يجب عرض العملة
  bool _shouldShowCurrency(String title) {
    final quantityTitles = [
      'إجمالي الكارتات',
      'أنواع البطاقات',
      'الرصيد المتبقي',
      'مخزون منخفض',
      'مخزون نافد',
    ];
    return !quantityTitles.contains(title);
  }

  // بطاقة إحصائية محسنة مع عرض أفضل لعدد الكارت
  Widget _buildEnhancedStatCard(
    String title,
    String value,
    Widget iconWidget,
    Color borderColor,
    int count,
    String unit,
  ) {
    return Container(
      padding: const EdgeInsets.all(20), // إضافة padding كبير
      decoration: BoxDecoration(
        gradient: title == 'إجمالي المبلغ'
            ? LinearGradient(
                colors: [Colors.white, Colors.blue.shade50],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              )
            : null,
        color: title == 'إجمالي المبلغ' ? null : Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: title == 'إجمالي المبلغ'
              ? Colors.blue.withValues(alpha: 0.3)
              : Colors.grey.withValues(alpha: 0.2),
          width: title == 'إجمالي المبلغ' ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: title == 'إجمالي المبلغ'
                ? Colors.blue.withValues(alpha: 0.2)
                : Colors.grey.withValues(alpha: 0.1),
            blurRadius: title == 'إجمالي المبلغ' ? 15 : 10,
            offset: title == 'إجمالي المبلغ'
                ? const Offset(0, 6)
                : const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              iconWidget,
              const Spacer(),
              // عرض عدد الكارت بحجم أصغر
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey.withValues(alpha: 0.2)),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.inventory_2_outlined,
                      size: 12,
                      color: Colors.grey.shade600,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${NumberFormatter.formatNumber(count)} ${_getCorrectUnit(unit, count)}',
                      style: TextStyle(
                        color: Colors.grey.shade700,
                        fontSize: 11,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          // المبلغ أو العدد مع العنوان
          Row(
            children: [
              Text(
                _getStatLabel(title),
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey.shade600,
                ),
              ),
              Expanded(
                child: FittedBox(
                  fit: BoxFit.scaleDown,
                  alignment: Alignment.centerLeft,
                  child: Text(
                    value,
                    style: const TextStyle(
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                    maxLines: 1,
                  ),
                ),
              ),
              if (_shouldShowCurrency(title)) ...[
                const SizedBox(width: 4),
                Text(
                  'د.ع',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade500,
                  ),
                ),
              ],
            ],
          ),
          const SizedBox(height: 8),
          // بطاقة بيضاء أنيقة حول النص
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.withValues(alpha: 0.2)),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.08),
                  blurRadius: 6,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: FittedBox(
              fit: BoxFit.scaleDown,
              child: Text(
                title,
                style: TextStyle(
                  color: Colors.grey.shade700,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
              ),
            ),
          ),
          const SizedBox(height: 8),
          // معلومة إضافية
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.access_time,
                size: 12,
                color: Colors.grey.shade400,
              ),
              const SizedBox(width: 4),
              Text(
                'محدث الآن',
                style: TextStyle(
                  fontSize: 10,
                  color: Colors.grey.shade400,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // بطاقات النظرة العامة المحسنة
  Widget _buildOverviewCards(Map<String, dynamic> statistics) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // العنوان الرئيسي
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.blue.shade600, Colors.blue.shade400],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.blue.withValues(alpha: 0.3),
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.dashboard,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'نظرة عامة على النشاط',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 20),

        // الصف الأول: إجمالي العملاء والعملاء النشطين
        Row(
          children: [
            Expanded(
              child: _buildProfessionalCustomersCard(
                  statistics['totalCustomers'] ?? 0),
            ),
          ],
        ),
      ],
    );
  }

  // بطاقة العملاء المحسنة
  Widget _buildProfessionalCustomersCard(int totalCustomers) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.white, Colors.blue.shade50],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.blue.withValues(alpha: 0.3), width: 2),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withValues(alpha: 0.2),
            blurRadius: 15,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Column(
        children: [
          // الأيقونة المحسنة في الأعلى
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.blue.shade500, Colors.blue.shade700],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.blue.withValues(alpha: 0.4),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: const Icon(
              Icons.people_rounded,
              color: Colors.white,
              size: 32,
            ),
          ),
          const SizedBox(height: 20),

          // الرقم الرئيسي مع تحسينات
          RichText(
            textAlign: TextAlign.center,
            text: TextSpan(
              children: [
                TextSpan(
                  text: NumberFormatter.formatNumber(totalCustomers),
                  style: TextStyle(
                    fontSize: 36,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue.shade800,
                  ),
                ),
                TextSpan(
                  text: ' عميل',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                    color: Colors.blue.shade600,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),

          // العنوان في بطاقة بيضاء
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.blue.withValues(alpha: 0.2)),
              boxShadow: [
                BoxShadow(
                  color: Colors.blue.withValues(alpha: 0.1),
                  blurRadius: 6,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Text(
              'إجمالي العملاء',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.blue.shade700,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  // إحصائيات السداد والديون المتأخرة
  Widget _buildPaymentStatistics() {
    return FutureBuilder<List<dynamic>>(
      future: Future.wait([
        _getTodayPaymentStats(),
        _getWeeklyPaymentStats(),
        _getMonthlyPaymentStats(),
      ]),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return _buildErrorWidget();
        }

        final results = snapshot.data ?? [null, null, null];
        final todayPayments = results[0] as Map<String, dynamic>? ?? {};
        final weeklyPayments = results[1] as Map<String, dynamic>? ?? {};
        final monthlyPayments = results[2] as Map<String, dynamic>? ?? {};

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان إحصائيات السداد المحسن
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.green.shade600, Colors.green.shade400],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.green.withValues(alpha: 0.3),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.payment,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  const Text(
                    'إحصائيات السداد والديون',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // سداد اليوم - بطاقة كبيرة
            _buildEnhancedStatCardWithDetails(
              'سداد اليوم',
              NumberFormatter.formatCurrency(
                (todayPayments['amount'] ?? 0.0).toInt(),
              ),
              _build3DIcon(Icons.today, Colors.green, Colors.lightGreen),
              Colors.grey,
              todayPayments['cardDetails'] ??
                  '${todayPayments['count'] ?? 0} كارت',
            ),

            const SizedBox(height: 16),

            // سداد الأسبوع - بطاقة كبيرة
            _buildEnhancedStatCard(
              'سداد الأسبوع',
              NumberFormatter.formatCurrency(
                (weeklyPayments['amount'] ?? 0.0).toInt(),
              ),
              _build3DIcon(Icons.date_range, Colors.blue, Colors.lightBlue),
              Colors.grey,
              weeklyPayments['count'] ?? 0,
              'كارت',
            ),

            const SizedBox(height: 16),

            // سداد الشهر - بطاقة كبيرة
            _buildEnhancedStatCard(
              'سداد الشهر',
              NumberFormatter.formatCurrency(
                (monthlyPayments['amount'] ?? 0.0).toInt(),
              ),
              _build3DIcon(
                Icons.calendar_month,
                Colors.purple,
                Colors.deepPurple,
              ),
              Colors.grey,
              monthlyPayments['count'] ?? 0,
              'كارت',
            ),
          ],
        );
      },
    );
  }

  // جلب إحصائيات سداد اليوم
  Future<Map<String, dynamic>> _getTodayPaymentStats() async {
    try {
      // جلب التسديدات من جدول payments بدلاً من debts
      final allPayments = await _databaseHelper.getAllPayments();
      final allDebts = await _databaseHelper.getAllDebts();
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final tomorrow = today.add(const Duration(days: 1));

      // فلترة التسديدات لليوم الحالي
      final todayPayments = allPayments.where((payment) {
        return payment.paymentDate
                .isAfter(today.subtract(const Duration(days: 1))) &&
            payment.paymentDate.isBefore(tomorrow);
      }).toList();

      // حساب تفاصيل الكارتات
      final cardTypeStats = <String, double>{};
      double totalAmount = 0;

      for (final payment in todayPayments) {
        totalAmount += payment.amount;

        // البحث عن الدين المرتبط بالتسديد
        final debt = allDebts.where((d) => d.id == payment.debtId).firstOrNull;

        if (debt != null) {
          final displayName = _getCardTypeDisplayName(debt.cardType);
          final paidRatio = payment.amount / debt.amount;
          final paidQuantity = debt.quantity * paidRatio;

          cardTypeStats[displayName] =
              (cardTypeStats[displayName] ?? 0) + paidQuantity;
        }
      }

      // تكوين نص التفاصيل
      String cardDetails = '';
      if (cardTypeStats.isNotEmpty) {
        final details = cardTypeStats.entries.map((entry) {
          final quantity = entry.value.round();
          return '${entry.key}: $quantity';
        }).join(' • ');
        cardDetails = details;
      } else {
        cardDetails = '${todayPayments.length} تسديد';
      }

      return {
        'count': todayPayments.length,
        'amount': totalAmount,
        'cardDetails': cardDetails,
      };
    } catch (e) {
      return {'count': 0, 'amount': 0.0, 'cardDetails': '0 تسديد'};
    }
  }

  // جلب إحصائيات سداد الأسبوع
  Future<Map<String, dynamic>> _getWeeklyPaymentStats() async {
    try {
      final allDebts = await _databaseHelper.getAllDebts();
      final now = DateTime.now();
      final weekStart = now.subtract(Duration(days: now.weekday - 1));
      final weekStartDay = DateTime(
        weekStart.year,
        weekStart.month,
        weekStart.day,
      );

      final weeklyPaidDebts = allDebts.where((debt) {
        return debt.status == DebtStatus.paid &&
            debt.entryDate.isAfter(
              weekStartDay.subtract(const Duration(days: 1)),
            ) &&
            debt.entryDate.isBefore(now.add(const Duration(days: 1)));
      }).toList();

      return {
        'count': weeklyPaidDebts.length,
        'amount': weeklyPaidDebts.fold<double>(
          0,
          (sum, debt) => sum + debt.amount,
        ),
      };
    } catch (e) {
      return {'count': 0, 'amount': 0.0};
    }
  }

  // جلب إحصائيات سداد الشهر
  Future<Map<String, dynamic>> _getMonthlyPaymentStats() async {
    try {
      final allDebts = await _databaseHelper.getAllDebts();
      final now = DateTime.now();
      final monthStart = DateTime(now.year, now.month);

      final monthlyPaidDebts = allDebts.where((debt) {
        return debt.status == DebtStatus.paid &&
            debt.entryDate.isAfter(
              monthStart.subtract(const Duration(days: 1)),
            ) &&
            debt.entryDate.isBefore(now.add(const Duration(days: 1)));
      }).toList();

      return {
        'count': monthlyPaidDebts.length,
        'amount': monthlyPaidDebts.fold<double>(
          0,
          (sum, debt) => sum + debt.amount,
        ),
      };
    } catch (e) {
      return {'count': 0, 'amount': 0.0};
    }
  }

  // نظرة عامة على المبيعات مع الكارتات المتأخرة
  Widget _buildSalesOverview(List<Map<String, dynamic>> salesData) {
    int totalQuantity = 0;
    double totalAmount = 0;

    for (var sale in salesData) {
      totalQuantity += sale['total_quantity'] as int? ?? 0;
      totalAmount += (sale['total_amount'] as num? ?? 0).toDouble();
    }

    return Container(
      margin: const EdgeInsets.only(top: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان ملخص المبيعات المحسن
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.orange.shade600, Colors.orange.shade400],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.orange.withValues(alpha: 0.3),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: const Icon(
                    Icons.summarize_outlined,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                const Text(
                  'ملخص المبيعات',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),

          // إجمالي المبلغ - بطاقة كبيرة (مع استقطاع ديون العملاء)
          _buildAdjustedTotalCard(totalAmount, totalQuantity),

          const SizedBox(height: 16),

          // إجمالي رأس المال - بطاقة تجمع المبيعات والمخزون
          _buildTotalCapitalCard(totalAmount),

          const SizedBox(height: 16),

          // إجمالي الكارتات - بطاقة كبيرة
          _buildEnhancedStatCard(
            'إجمالي الكارتات',
            '${NumberFormatter.formatNumber(totalQuantity)} كارت',
            _build3DIcon(Icons.inventory_2, Colors.green, Colors.lightGreen),
            Colors.grey,
            totalQuantity,
            'كارت',
          ),

          const SizedBox(height: 16),

          // الكارتات المتأخرة - بطاقة كبيرة
          FutureBuilder<Map<String, dynamic>>(
            future: _getOverdueCardsStats(),
            builder: (context, snapshot) {
              final overdueStats = snapshot.data ??
                  {'count': 0, 'amount': 0.0, 'percentage': 0.0};
              return _buildEnhancedStatCard(
                'الكارتات المتأخرة',
                NumberFormatter.formatCurrency(
                  (overdueStats['amount'] as double).toInt(),
                ),
                _build3DIcon(
                  Icons.warning_rounded,
                  Colors.red,
                  Colors.deepOrange,
                ),
                Colors.grey,
                overdueStats['count'] as int,
                'كارت',
              );
            },
          ),

          const SizedBox(height: 16),

          // ديون العملاء - بطاقة جديدة
          FutureBuilder<Map<String, dynamic>>(
            future: _getCustomerDebtsStats(),
            builder: (context, snapshot) {
              final debtStats = snapshot.data ??
                  {'count': 0, 'amount': 0.0, 'netAmount': 0.0};
              return _buildCustomerDebtCard(debtStats);
            },
          ),
        ],
      ),
    );
  }

  // جلب إحصائيات الكارتات المتأخرة
  Future<Map<String, dynamic>> _getOverdueCardsStats() async {
    try {
      final allDebts = await _databaseHelper.getAllDebts();
      final now = DateTime.now();

      final overdueDebts = allDebts.where((debt) {
        return debt.status != DebtStatus.paid && debt.dueDate.isBefore(now);
      }).toList();

      double totalAmount = 0;
      int totalCount = 0;

      for (final debt in overdueDebts) {
        totalAmount += debt.remainingAmount;
        totalCount += 1; // عدد الديون المتأخرة وليس الكمية
      }

      // حساب النسبة المئوية من إجمالي الديون
      final allDebtsStats = await _databaseHelper.getStatistics();
      final totalDebtsAmount = (allDebtsStats['totalAmount'] ?? 0.0) as double;
      final percentage =
          totalDebtsAmount > 0 ? (totalAmount / totalDebtsAmount) * 100 : 0.0;

      return {
        'amount': totalAmount,
        'count': totalCount,
        'percentage': percentage,
      };
    } catch (e) {
      debugPrint('خطأ في جلب إحصائيات الكارتات المتأخرة: $e');
      return {'amount': 0.0, 'count': 0, 'percentage': 0.0};
    }
  }

  // جلب إحصائيات ديون العملاء
  Future<Map<String, dynamic>> _getCustomerDebtsStats() async {
    try {
      final allDebts = await _databaseHelper.getAllDebts();

      // فلترة الديون التي يدين بها العملاء (اتجاه iOweCustomer)
      final customerDebts = allDebts.where((debt) {
        return debt.direction == DebtDirection.iOweCustomer;
      }).toList();

      double totalAmount = 0;
      double totalPaidAmount = 0;
      final int totalCount = customerDebts.length;

      for (final debt in customerDebts) {
        totalAmount += debt.remainingAmount;
        totalPaidAmount += debt.paidAmount;
      }

      // حساب المبلغ الصافي (المبلغ الإجمالي - المدفوع)
      final netAmount = totalAmount - totalPaidAmount;

      return {
        'amount': totalAmount,
        'paidAmount': totalPaidAmount,
        'netAmount': netAmount,
        'count': totalCount,
      };
    } catch (e) {
      debugPrint('خطأ في جلب إحصائيات ديون العملاء: $e');
      return {'amount': 0.0, 'paidAmount': 0.0, 'netAmount': 0.0, 'count': 0};
    }
  }

  // بطاقة إجمالي رأس المال (المبيعات + المخزون الاحتياطي)
  Widget _buildTotalCapitalCard(double salesAmount) {
    return Consumer2<DebtProvider, CardInventoryProvider>(
      builder: (context, debtProvider, inventoryProvider, child) {
        // حساب إجمالي مبلغ المخزون الاحتياطي
        double inventoryValue = 0;
        for (final inventory in inventoryProvider.inventories) {
          inventoryValue += inventory.totalValue;
        }

        return FutureBuilder<double>(
          future: _calculateCustomerDebtsAmount(),
          builder: (context, snapshot) {
            final customerDebtsAmount = snapshot.data ?? 0.0;
            final adjustedSalesAmount = salesAmount - customerDebtsAmount;
            final totalCapital = adjustedSalesAmount + inventoryValue;

            return Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: Colors.teal,
                  width: 2,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.teal.withValues(alpha: 0.2),
                    blurRadius: 15,
                    offset: const Offset(0, 6),
                  ),
                ],
              ),
              child: Column(
                children: [
                  // العنوان والأيقونة
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.teal.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Icon(
                          Icons.account_balance,
                          size: 28,
                          color: Colors.teal,
                        ),
                      ),
                      const SizedBox(width: 12),
                      const Expanded(
                        child: Text(
                          'إجمالي رأس المال',
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.teal,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // المبلغ الإجمالي
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.baseline,
                    textBaseline: TextBaseline.alphabetic,
                    children: [
                      Text(
                        NumberFormatter.formatCurrency(totalCapital.toInt()),
                        style: const TextStyle(
                          fontSize: 32,
                          fontWeight: FontWeight.bold,
                          color: Colors.teal,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'ألف',
                        style: TextStyle(
                          fontSize: 18,
                          color: Colors.teal.shade600,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // تفصيل المكونات
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.teal.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Colors.teal.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'صافي المبيعات:',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.teal.shade700,
                              ),
                            ),
                            Text(
                              '${NumberFormatter.formatCurrency(adjustedSalesAmount.toInt())} ألف',
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: Colors.teal,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'قيمة المخزون:',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.teal.shade700,
                              ),
                            ),
                            Text(
                              '${NumberFormatter.formatCurrency(inventoryValue.toInt())} ألف',
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: Colors.teal,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  // بطاقة إجمالي المبلغ مع استقطاع ديون العملاء
  Widget _buildAdjustedTotalCard(double totalAmount, int totalQuantity) {
    return FutureBuilder<double>(
      future: _calculateCustomerDebtsAmount(),
      builder: (context, snapshot) {
        final customerDebtsAmount = snapshot.data ?? 0.0;
        final adjustedTotal = totalAmount - customerDebtsAmount;

        return _buildEnhancedStatCard(
          'إجمالي المبلغ',
          NumberFormatter.formatCurrency(adjustedTotal.toInt()),
          _build3DIcon(
            Icons.attach_money,
            Colors.blue.shade500,
            Colors.blue.shade700,
          ),
          Colors.blue,
          totalQuantity,
          'كارت',
        );
      },
    );
  }

  // حساب مبلغ ديون العملاء من الاستقطاعات الثابتة (لا يتأثر بالحذف)
  Future<double> _calculateCustomerDebtsAmount() async {
    try {
      // جلب الاستقطاعات الثابتة بدلاً من الديون
      final totalPermanentDeductions =
          await _databaseHelper.getTotalPermanentDeductions();
      return totalPermanentDeductions;
    } catch (e) {
      debugPrint('خطأ في حساب الاستقطاعات الثابتة: $e');
      return 0.0;
    }
  }

  // بطاقة ديون العملاء المخصصة
  Widget _buildCustomerDebtCard(Map<String, dynamic> debtStats) {
    final totalAmount = debtStats['amount'] as double? ?? 0.0;
    final paidAmount = debtStats['paidAmount'] as double? ?? 0.0;
    final count = debtStats['count'] as int? ?? 0;

    return FutureBuilder<double>(
      future: _calculateCustomerDebtsAmount(),
      builder: (context, snapshot) {
        final permanentDeductions = snapshot.data ?? 0.0;

        return Container(
          padding: const EdgeInsets.all(18),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: Colors.red.withValues(alpha: 0.2),
              width: 1.5,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.red.withValues(alpha: 0.1),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // العنوان مع الأيقونة
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [Colors.red.shade400, Colors.red.shade600],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.red.withValues(alpha: 0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.remove_circle,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'ديون العملاء',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                        ),
                        Text(
                          '$count دين (استقطاع)',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.red.shade600,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // المبلغ الإجمالي
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'إجمالي الديون:',
                    style: TextStyle(fontSize: 14, color: Colors.grey.shade700),
                  ),
                  Text(
                    '${NumberFormatter.formatCurrency(totalAmount.toInt())} ألف',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Colors.red.shade700,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),

              // المبلغ المدفوع
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'المدفوع:',
                    style: TextStyle(fontSize: 14, color: Colors.grey.shade700),
                  ),
                  Text(
                    '${NumberFormatter.formatCurrency(paidAmount.toInt())} ألف',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Colors.green.shade600,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),

              // خط فاصل
              Divider(color: Colors.grey.shade300),
              const SizedBox(height: 8),

              // الاستقطاع الإجمالي (يبقى مستقطعاً حتى بعد الدفع)
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'إجمالي الاستقطاع:',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [Colors.red.shade400, Colors.red.shade600],
                      ),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '- ${NumberFormatter.formatCurrency(permanentDeductions.toInt())} ألف',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  // نظرة عامة على المخزون
  Widget _buildStockOverview(Map<String, dynamic> stockData) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان ملخص المخزون المحسن
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.teal.shade600, Colors.teal.shade400],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.teal.withValues(alpha: 0.3),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(
                  Icons.inventory_2,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              const Text(
                'ملخص المخزون',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),

        // بطاقات المخزون
        Column(
          children: [
            // أنواع البطاقات - صف عريض
            _buildEnhancedStatCard(
              'أنواع البطاقات',
              NumberFormatter.formatNumber(stockData['totalCardTypes'] ?? 0),
              _build3DIcon(Icons.category, Colors.blue, Colors.lightBlue),
              Colors.grey,
              stockData['totalCardTypes'] ?? 0,
              'نوع',
            ),
            const SizedBox(height: 12),

            // الرصيد المتبقي - صف عريض
            _buildEnhancedStatCard(
              'الرصيد المتبقي',
              NumberFormatter.formatNumber(stockData['totalQuantity'] ?? 0),
              _build3DIcon(Icons.inventory, Colors.green, Colors.lightGreen),
              Colors.grey,
              stockData['totalQuantity'] ?? 0,
              'بطاقة',
            ),
            const SizedBox(height: 12),

            // الصف الثالث - مخزون منخفض ونافد جنباً إلى جنب
            LayoutBuilder(
              builder: (context, constraints) {
                final availableWidth = constraints.maxWidth;
                final cardWidth = (availableWidth - 12) / 2;

                return Row(
                  children: [
                    // مخزون منخفض
                    SizedBox(
                      width: cardWidth,
                      child: _buildEnhancedStatCard(
                        'مخزون منخفض',
                        NumberFormatter.formatNumber(
                            stockData['lowStockCount'] ?? 0),
                        _build3DIcon(
                            Icons.warning, Colors.orange, Colors.deepOrange),
                        Colors.grey,
                        stockData['lowStockCount'] ?? 0,
                        'نوع',
                      ),
                    ),
                    const SizedBox(width: 12),
                    // مخزون نافد
                    SizedBox(
                      width: cardWidth,
                      child: _buildEnhancedStatCard(
                        'مخزون نافد',
                        NumberFormatter.formatNumber(
                            stockData['outOfStockCount'] ?? 0),
                        _build3DIcon(
                            Icons.error, Colors.red, Colors.deepOrange),
                        Colors.grey,
                        stockData['outOfStockCount'] ?? 0,
                        'نوع',
                      ),
                    ),
                  ],
                );
              },
            ),
          ],
        ),

        const SizedBox(height: 16),

        // بطاقة مبلغ المخزون الاحتياطي - عريضة
        Consumer<CardInventoryProvider>(
          builder: (context, inventoryProvider, child) {
            // حساب إجمالي مبلغ المخزون
            double totalInventoryValue = 0;
            for (final inventory in inventoryProvider.inventories) {
              totalInventoryValue += inventory.totalValue;
            }

            return _buildInventoryValueCard(totalInventoryValue);
          },
        ),
      ],
    );
  }

  // بطاقة مبلغ المخزون الاحتياطي
  Widget _buildInventoryValueCard(double totalAmount) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border:
            Border.all(color: Colors.teal.withValues(alpha: 0.3), width: 1.5),
        boxShadow: [
          BoxShadow(
            color: Colors.teal.withValues(alpha: 0.1),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // العنوان والأيقونة في الأعلى
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.teal.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.account_balance_wallet,
                  size: 28,
                  color: Colors.teal.shade600,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'مبلغ المخزون الاحتياطي',
                  style: TextStyle(
                    fontSize: 18,
                    color: Colors.grey.shade800,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // القيمة والوحدة في صف واحد
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.baseline,
            textBaseline: TextBaseline.alphabetic,
            children: [
              Text(
                NumberFormatter.formatCurrency(totalAmount.toInt()),
                style: TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  color: Colors.teal.shade700,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                'ألف',
                style: TextStyle(
                  fontSize: 18,
                  color: Colors.teal.shade600,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          // تسمية احتياطي
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.teal.shade50,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: Colors.teal.shade200),
            ),
            child: Text(
              'احتياطي',
              style: TextStyle(
                fontSize: 12,
                color: Colors.teal.shade700,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // الحصول على لون نوع البطاقة
  Color _getCardTypeColor(String cardType) {
    switch (cardType.toLowerCase()) {
      case 'زين':
        return Colors.purple;
      case 'آسيا':
        return Colors.red;
      case 'أبو الستة':
        return Colors.grey;
      case 'أبو العشرة':
        return Colors.teal;
      default:
        return Colors.blue;
    }
  }

  // الحصول على اسم عرض نوع البطاقة
  String _getCardTypeDisplayName(String cardType) {
    // التحقق من أن cardType ليس فارغ أو null
    if (cardType.isEmpty) {
      return 'غير محدد';
    }

    // إزالة كلمة "متعدد:" إذا كانت موجودة
    String cleanCardType = cardType;
    if (cardType.startsWith('متعدد: ')) {
      cleanCardType = cardType.substring(7); // إزالة "متعدد: "
    }

    // استخدام CardTypeProvider للحصول على الاسم الصحيح
    final cardTypeProvider = Provider.of<CardTypeProvider>(
      context,
      listen: false,
    );

    // أولاً، نحاول البحث بالمعرف المباشر
    CardTypeOption? cardTypeOption = cardTypeProvider.getCardTypeById(
      cleanCardType,
    );

    // إذا لم نجد، نحاول البحث في الأنواع الافتراضية بطرق مختلفة
    if (cardTypeOption == null) {
      try {
        cardTypeOption =
            cardTypeProvider.allCardTypes.cast<CardTypeOption?>().firstWhere(
                  (type) =>
                      type != null &&
                      (type.id.toLowerCase() == cleanCardType.toLowerCase() ||
                          type.defaultType?.name.toLowerCase() ==
                              cleanCardType.toLowerCase() ||
                          (type.defaultType != null &&
                              _compareCardTypes(
                                type.defaultType!.name,
                                cleanCardType,
                              ))),
                  orElse: () => null,
                );
      } catch (e) {
        // في حالة حدوث خطأ، نستخدم التحويل اليدوي
        cardTypeOption = null;
      }
    }

    if (cardTypeOption != null && cardTypeOption.displayName.isNotEmpty) {
      return cardTypeOption.displayName;
    }

    // إذا لم نجد النوع، نحاول التحويل اليدوي
    final convertedName = _convertCardTypeToArabic(cleanCardType);
    return convertedName.isNotEmpty ? convertedName : 'غير محدد';
  }

  // مقارنة أنواع البطاقات مع مراعاة الاختلافات
  bool _compareCardTypes(String enumName, String dbValue) {
    final cleanEnum = enumName.toLowerCase();
    final cleanDb = dbValue.toLowerCase();

    // مقارنات خاصة للأنواع المختلفة
    if ((cleanEnum == 'sia' && (cleanDb == 'asia' || cleanDb == 'آسيا')) ||
        (cleanEnum == 'asia' && (cleanDb == 'sia' || cleanDb == 'آسيا')) ||
        (cleanDb == 'sia' && cleanEnum == 'asia') ||
        (cleanDb == 'asia' && cleanEnum == 'sia')) {
      return true;
    }

    if ((cleanEnum == 'abuashara' &&
            (cleanDb.contains('ashara') || cleanDb.contains('عشرة'))) ||
        (cleanEnum == 'abusitta' &&
            (cleanDb.contains('sitta') || cleanDb.contains('ستة')))) {
      return true;
    }

    return cleanEnum == cleanDb;
  }

  // تحويل أسماء البطاقات إلى عربية
  String _convertCardTypeToArabic(String cardType) {
    if (cardType.isEmpty) return 'غير محدد';

    final cleanType = cardType.trim().toLowerCase();

    switch (cleanType) {
      case 'cash':
        return 'نقدي';
      case 'visa':
        return 'فيزا';
      case 'mastercard':
        return 'ماستركارد';
      case 'americanexpress':
        return 'أمريكان إكسبريس';
      case 'zain':
        return 'زين';
      case 'sia':
      case 'asia': // إضافة دعم لـ asia أيضاً
        return 'آسيا';
      case 'abuashara':
        return 'أبو العشرة';
      case 'abusitta':
        return 'أبو الستة';
      case 'other':
        return 'أخرى';
      // إضافة المزيد من الأنواع الشائعة
      case 'custom_zain':
        return 'زين';
      case 'custom_asia':
      case 'custom_sia':
        return 'آسيا';
      case 'custom_abuashara':
        return 'أبو العشرة';
      case 'custom_abusitta':
        return 'أبو الستة';
      default:
        // إذا كان النص عربي، أعده كما هو
        if (_isArabicText(cardType)) {
          return cardType.trim();
        }
        // إذا كان يبدأ بـ custom_ نحاول استخراج الاسم
        if (cleanType.startsWith('custom_')) {
          final extractedName = cleanType.substring(7); // إزالة 'custom_'
          return _convertCardTypeToArabic(extractedName);
        }
        // إذا كان إنجليزي ولم نجده، نعيد الاسم الأصلي مع تحسين التنسيق
        return _capitalizeFirst(cardType.trim());
    }
  }

  // تحسين تنسيق النص
  String _capitalizeFirst(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1).toLowerCase();
  }

  // فحص إذا كان النص عربي
  bool _isArabicText(String text) {
    if (text.isEmpty) return false;
    return RegExp(r'[\u0600-\u06FF]').hasMatch(text);
  }

  // دالة للحصول على الوحدة الصحيحة حسب العدد
  String _getCorrectUnit(String unit, int count) {
    if (unit == 'كارت' || unit == 'بطاقة') {
      return 'كارت';
    }
    return unit;
  }

  // تحليل مبيعات أنواع البطاقات
  Widget _buildCardTypeSalesAnalysis(List<Map<String, dynamic>> salesData) {
    if (salesData.isEmpty) {
      return _buildEmptyAnalysisCard();
    }

    // حساب إجمالي المبيعات
    final totalAmount = salesData.fold<double>(
      0.0,
      (sum, sale) => sum + ((sale['total_amount'] as num?) ?? 0).toDouble(),
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان تحليل المبيعات المحسن
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.indigo.shade600, Colors.indigo.shade400],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.indigo.withValues(alpha: 0.3),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(
                  Icons.analytics,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              const Expanded(
                child: Text(
                  'تحليل المبيعات حسب نوع البطاقة',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),

        // بطاقات المبيعات
        ...salesData
            .take(4)
            .map((sale) => _buildSalesAnalysisCard(sale, totalAmount)),

        if (salesData.length > 4)
          Container(
            margin: const EdgeInsets.only(top: 12),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.more_horiz, color: Colors.grey.shade600),
                const SizedBox(width: 8),
                Text(
                  'و ${salesData.length - 4} أنواع أخرى',
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  // بطاقة تحليل مبيعات
  Widget _buildSalesAnalysisCard(
    Map<String, dynamic> sale,
    double totalAmount,
  ) {
    final cardType = sale['card_type'] as String? ?? 'غير محدد';
    final count = sale['count'] as int? ?? 0;
    final quantity = sale['total_quantity'] as int? ?? 0;
    final amount = (sale['total_amount'] as num? ?? 0).toDouble();
    final percentage = totalAmount > 0 ? (amount / totalAmount) * 100 : 0.0;

    final cardColor = _getCardTypeColor(cardType);
    final displayName = _getCardTypeDisplayName(cardType);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [BoxShadow(color: cardColor.withValues(alpha: 0.1))],
        border: Border.all(color: cardColor.withValues(alpha: 0.2)),
      ),
      child: Column(
        children: [
          // Header مع اسم البطاقة والنسبة
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: cardColor.withValues(alpha: 0.05),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: cardColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(Icons.credit_card, color: cardColor, size: 20),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    displayName,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87, // لون أسود ثابت للنص لضمان الوضوح
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: cardColor,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${percentage.toStringAsFixed(1)}%',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // المحتوى مع الإحصائيات
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // العمليات
                Expanded(
                  child: _buildMiniStatItem(
                    'العمليات',
                    count.toString(),
                    Icons.receipt,
                    Colors.blue,
                  ),
                ),
                Container(width: 1, height: 40, color: Colors.grey.shade200),
                // الكمية
                Expanded(
                  child: _buildMiniStatItem(
                    'الكمية',
                    quantity.toString(),
                    Icons.inventory_2,
                    Colors.green,
                  ),
                ),
                Container(width: 1, height: 40, color: Colors.grey.shade200),
                // المبلغ
                Expanded(
                  child: _buildMiniStatItem(
                    'المبلغ',
                    NumberFormatter.formatCurrency(amount.toInt()),
                    Icons.attach_money,
                    cardColor,
                  ),
                ),
              ],
            ),
          ),

          // شريط التقدم
          Container(
            margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
            height: 6,
            decoration: BoxDecoration(
              color: Colors.grey.shade200,
              borderRadius: BorderRadius.circular(3),
            ),
            child: FractionallySizedBox(
              alignment: Alignment.centerLeft,
              widthFactor: percentage / 100,
              child: Container(
                decoration: BoxDecoration(
                  color: cardColor,
                  borderRadius: BorderRadius.circular(3),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // عنصر إحصائية صغير
  Widget _buildMiniStatItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Icon(icon, color: color, size: 16),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: Colors.black87, // لون أسود ثابت للنص لضمان الوضوح
          ),
        ),
        Text(
          label,
          style: TextStyle(fontSize: 10, color: Colors.grey.shade600),
        ),
      ],
    );
  }

  // بطاقة فارغة للتحليل
  Widget _buildEmptyAnalysisCard() {
    return Container(
      padding: const EdgeInsets.all(32),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        children: [
          Icon(Icons.analytics_outlined, size: 48, color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text(
            'لا توجد بيانات مبيعات',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ابدأ بإضافة ديون لرؤية التحليل',
            style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
          ),
        ],
      ),
    );
  }

  // مخطط توزيع البطاقات الجديد
  Widget _buildInventoryDistributionChart(
    CardInventoryProvider inventoryProvider,
    CardTypeProvider cardTypeProvider,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Icon(Icons.bar_chart, color: Colors.green.shade600, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'توزيع المخزون',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          ],
        ),
        const SizedBox(height: 16),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [BoxShadow(color: Colors.grey.withValues(alpha: 0.1))],
          ),
          child: _buildInventoryBarChart(inventoryProvider, cardTypeProvider),
        ),
      ],
    );
  }

  // مخطط أعمدة المخزون الجديد - أفقي واحترافي مع النسب المئوية
  Widget _buildInventoryBarChart(
    CardInventoryProvider inventoryProvider,
    CardTypeProvider cardTypeProvider,
  ) {
    if (inventoryProvider.inventories.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(32),
        child: Column(
          children: [
            Icon(
              Icons.inventory_2_outlined,
              size: 48,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد بيانات لعرضها',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    }

    final maxQuantity = inventoryProvider.inventories
        .map((s) => s.quantity)
        .reduce((a, b) => a > b ? a : b);

    // حساب إجمالي الكمية للنسب المئوية
    final totalQuantity = inventoryProvider.inventories.fold<int>(
      0,
      (sum, inventory) => sum + inventory.quantity,
    );

    if (maxQuantity == 0) {
      return Container(
        padding: const EdgeInsets.all(32),
        child: Column(
          children: [
            Icon(
              Icons.warning_outlined,
              size: 48,
              color: Colors.orange.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'جميع البطاقات نافدة',
              style: TextStyle(
                fontSize: 16,
                color: Colors.orange.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    }

    // استخدام البطاقات مباشرة
    final sortedInventories = inventoryProvider.inventories;

    return Column(
      children: sortedInventories.map((inventory) {
        final cardName = _getCardTypeDisplayName(inventory.cardType);
        final percentage = inventory.quantity / maxQuantity; // للشريط
        final percentageOfTotal = totalQuantity > 0
            ? (inventory.quantity / totalQuantity) * 100
            : 0.0; // للنسبة المئوية
        final color = _getInventoryCardColor(inventory);

        return Container(
          margin: const EdgeInsets.only(bottom: 20),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: color.withValues(alpha: 0.2)),
            boxShadow: [
              BoxShadow(
                color: color.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // الصف العلوي: اسم البطاقة والنسبة المئوية
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Row(
                      children: [
                        Container(
                          width: 12,
                          height: 12,
                          decoration: BoxDecoration(
                            color: color,
                            borderRadius: BorderRadius.circular(2),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            cardName,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 10,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: color,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${percentageOfTotal.toStringAsFixed(1)}%',
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // الصف الثاني: الكمية والشريط الأفقي
              Row(
                children: [
                  // الكمية
                  SizedBox(
                    width: 80,
                    child: Text(
                      '${inventory.quantity} بطاقة',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: color,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),

                  // الشريط الأفقي
                  Expanded(
                    child: Container(
                      height: 16,
                      decoration: BoxDecoration(
                        color: Colors.grey.shade200,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: FractionallySizedBox(
                        alignment: Alignment.centerLeft,
                        widthFactor: percentage,
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            gradient: LinearGradient(
                              colors: [color.withValues(alpha: 0.7), color],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),

                  // القيمة القصوى
                  Text(
                    '$maxQuantity',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  // الحصول على لون بطاقة المخزون الجديد
  Color _getInventoryCardColor(CardInventory inventory) {
    if (inventory.quantity == 0) return Colors.red;
    if (inventory.isLowStock) return Colors.orange;
    return Colors.green;
  }

  // مخطط المبيعات السنوي الاحترافي
  Widget _buildYearlySalesChart() {
    return FutureBuilder<Map<String, dynamic>>(
      future: _getYearlySalesData(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Container(
            height: 400,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: const Center(child: CircularProgressIndicator()),
          );
        }

        if (snapshot.hasError) {
          return _buildErrorWidget();
        }

        final yearlyData = snapshot.data ?? {};
        final monthlyData =
            yearlyData['monthlyData'] as List<Map<String, dynamic>>? ?? [];
        final totalYearSales = yearlyData['totalYearSales'] as double? ?? 0.0;
        final bestMonth =
            yearlyData['bestMonth'] as Map<String, dynamic>? ?? {};
        final worstMonth =
            yearlyData['worstMonth'] as Map<String, dynamic>? ?? {};

        return Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.white,
                Colors.blue.shade50.withValues(alpha: 0.3),
              ],
            ),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: Colors.blue.withValues(alpha: 0.1)),
            boxShadow: [
              BoxShadow(
                color: Colors.blue.withValues(alpha: 0.08),
                blurRadius: 15,
                offset: const Offset(0, 6),
                spreadRadius: 1,
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // العنوان الرئيسي
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [Colors.indigo.shade600, Colors.indigo.shade400],
                  ),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.indigo.withValues(alpha: 0.3),
                      blurRadius: 12,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: const Icon(
                        Icons.trending_up,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    const Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'تحليل المبيعات السنوي',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                          SizedBox(height: 4),
                          Text(
                            'الاتجاهات والنسب الشهرية',
                            style: TextStyle(
                              fontSize: 13,
                              color: Colors.white70,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 20),

              // إحصائيات سريعة
              Row(
                children: [
                  Expanded(
                    child: _buildQuickStatCard(
                      'الأقوى',
                      bestMonth['name'] ?? 'غير محدد',
                      NumberFormatter.formatCurrency(
                        (bestMonth['amount'] ?? 0.0).toInt(),
                      ),
                      Icons.trending_up,
                      Colors.green,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildQuickStatCard(
                      'الأضعف',
                      worstMonth['name'] ?? 'غير محدد',
                      NumberFormatter.formatCurrency(
                        (worstMonth['amount'] ?? 0.0).toInt(),
                      ),
                      Icons.trending_down,
                      Colors.red,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 20),

              // المخطط الشريطي
              if (monthlyData.isNotEmpty)
                _buildMonthlyChart(monthlyData, totalYearSales),
            ],
          ),
        );
      },
    );
  }

  // بطاقة إحصائية سريعة
  Widget _buildQuickStatCard(
    String title,
    String subtitle,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withValues(alpha: 0.2)),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(icon, color: color, size: 20),
              ),
              const Spacer(),
              Text(
                title,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            subtitle,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  // المخطط الشريطي الشهري
  Widget _buildMonthlyChart(
    List<Map<String, dynamic>> monthlyData,
    double totalYearSales,
  ) {
    final maxAmount = monthlyData.isNotEmpty
        ? monthlyData
            .map((m) => m['amount'] as double)
            .reduce((a, b) => a > b ? a : b)
        : 0.0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'المبيعات الشهرية',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 200,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: monthlyData.length,
            itemBuilder: (context, index) {
              final month = monthlyData[index];
              final amount = month['amount'] as double;
              final percentage =
                  totalYearSales > 0 ? (amount / totalYearSales) * 100 : 0.0;
              final heightPercentage = maxAmount > 0 ? amount / maxAmount : 0.0;
              final monthName = month['name'] as String;

              Color barColor = Colors.blue.shade400;
              if (percentage > 12) {
                barColor = Colors.green.shade500; // أقوى من المتوسط
              } else if (percentage < 6) {
                barColor = Colors.red.shade400; // أضعف من المتوسط
              } else if (percentage < 9) {
                barColor = Colors.orange.shade400; // متوسط منخفض
              }

              return GestureDetector(
                onTap: () => _showMonthlyDetailsDialog(
                  context,
                  month['month'] as int,
                  DateTime.now().year,
                ),
                child: Container(
                  width: 60,
                  margin: const EdgeInsets.only(right: 8),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      // النسبة المئوية
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 4,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: barColor.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Text(
                          '${percentage.toStringAsFixed(1)}%',
                          style: TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.w600,
                            color: barColor,
                          ),
                        ),
                      ),
                      const SizedBox(height: 8),

                      // الشريط
                      Expanded(
                        child: Container(
                          width: 32,
                          alignment: Alignment.bottomCenter,
                          child: Container(
                            width: 32,
                            height: 35 * heightPercentage,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.bottomCenter,
                                end: Alignment.topCenter,
                                colors: [
                                  barColor,
                                  barColor.withValues(alpha: 0.7),
                                ],
                              ),
                              borderRadius: BorderRadius.circular(6),
                              boxShadow: [
                                BoxShadow(
                                  color: barColor.withValues(alpha: 0.3),
                                  blurRadius: 4,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 8),

                      // اسم الشهر
                      Text(
                        monthName,
                        style: TextStyle(
                          fontSize: 11,
                          fontWeight: FontWeight.w500,
                          color: Colors.grey.shade700,
                        ),
                      ),

                      // المبلغ
                      Text(
                        NumberFormatter.formatCurrency(amount.toInt()),
                        style: TextStyle(
                          fontSize: 9,
                          color: Colors.grey.shade600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),

        const SizedBox(height: 16),

        // مفتاح الألوان
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildLegendItem('أقوى', Colors.green.shade500),
            _buildLegendItem('متوسط', Colors.blue.shade400),
            _buildLegendItem('منخفض', Colors.orange.shade400),
            _buildLegendItem('ضعيف', Colors.red.shade400),
          ],
        ),
      ],
    );
  }

  // عنصر مفتاح الألوان
  Widget _buildLegendItem(String label, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(width: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 11,
            color: Colors.grey.shade700,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  // جلب بيانات المبيعات السنوية
  Future<Map<String, dynamic>> _getYearlySalesData() async {
    try {
      final allDebts = await _databaseHelper.getAllDebts();
      final currentYear = DateTime.now().year;

      // فلترة الديون للسنة الحالية
      final yearDebts = allDebts.where((debt) {
        return debt.entryDate.year == currentYear;
      }).toList();

      // تجميع البيانات حسب الشهر
      final Map<int, Map<String, dynamic>> monthlyStats =
          <int, Map<String, dynamic>>{};

      for (int month = 1; month <= 12; month++) {
        final monthDebts = yearDebts.where((debt) {
          return debt.entryDate.month == month;
        }).toList();

        final monthAmount = monthDebts.fold<double>(
          0,
          (sum, debt) => sum + debt.amount,
        );

        monthlyStats[month] = {
          'month': month,
          'name': _getArabicMonthName(month),
          'amount': monthAmount,
          'count': monthDebts.length,
        };
      }

      // حساب الإجماليات
      final totalYearSales = monthlyStats.values.fold<double>(
        0,
        (sum, month) => sum + (month['amount'] as double),
      );

      // العثور على أقوى وأضعف شهر
      final sortedMonths = monthlyStats.values.toList()
        ..sort(
          (a, b) => (b['amount'] as double).compareTo(a['amount'] as double),
        );

      final bestMonth = sortedMonths.isNotEmpty ? sortedMonths.first : {};
      final worstMonth = sortedMonths.isNotEmpty ? sortedMonths.last : {};

      return {
        'monthlyData': monthlyStats.values.toList(),
        'totalYearSales': totalYearSales,
        'bestMonth': bestMonth,
        'worstMonth': worstMonth,
      };
    } catch (e) {
      return {
        'monthlyData': <Map<String, dynamic>>[],
        'totalYearSales': 0.0,
        'bestMonth': <String, dynamic>{},
        'worstMonth': <String, dynamic>{},
      };
    }
  }

  // الحصول على اسم الشهر بالعربية
  String _getArabicMonthName(int month) {
    const monthNames = [
      '',
      'الأول',
      'الثاني',
      'الثالث',
      'الرابع',
      'الخامس',
      'السادس',
      'السابع',
      'الثامن',
      'التاسع',
      'العاشر',
      'الحادي عشر',
      'الثاني عشر',
    ];
    return month >= 1 && month <= 12 ? monthNames[month] : 'غير محدد';
  }

  // جلب تفاصيل مبيعات شهر معين
  Future<Map<String, dynamic>> _getMonthlyDetails(int month, int year) async {
    try {
      final allDebts = await _databaseHelper.getAllDebts();

      // فلترة الديون للشهر والسنة المحددة (المبيعات فقط)
      final monthDebts = allDebts.where((debt) {
        return debt.entryDate.month == month &&
            debt.entryDate.year == year &&
            debt.direction == DebtDirection.customerOwesMe;
      }).toList();

      // حساب الإجماليات
      final totalAmount =
          monthDebts.fold<double>(0, (sum, debt) => sum + debt.amount);
      final totalCards =
          monthDebts.fold<int>(0, (sum, debt) => sum + debt.remainingQuantity);

      // تجميع البيانات حسب نوع الكارت
      final Map<String, Map<String, dynamic>> cardTypeStats = {};

      for (final debt in monthDebts) {
        final cardType = debt.cardType;

        if (cardTypeStats.containsKey(cardType)) {
          cardTypeStats[cardType]!['count'] =
              (cardTypeStats[cardType]!['count'] as int) + 1;
          cardTypeStats[cardType]!['quantity'] =
              (cardTypeStats[cardType]!['quantity'] as int) + debt.quantity;
          cardTypeStats[cardType]!['amount'] =
              (cardTypeStats[cardType]!['amount'] as double) + debt.amount;
        } else {
          cardTypeStats[cardType] = {
            'cardType': cardType,
            'count': 1,
            'quantity': debt.quantity,
            'amount': debt.amount,
          };
        }
      }

      // حساب النسب المئوية
      for (final stats in cardTypeStats.values) {
        stats['percentage'] =
            totalAmount > 0 ? (stats['amount'] / totalAmount) * 100 : 0.0;
      }

      // ترتيب حسب المبلغ
      final sortedCardTypes = cardTypeStats.values.toList()
        ..sort(
            (a, b) => (b['amount'] as double).compareTo(a['amount'] as double));

      return {
        'monthName': _getArabicMonthName(month),
        'totalAmount': totalAmount,
        'totalCards': totalCards,
        'cardTypeStats': sortedCardTypes,
        'debtsCount': monthDebts.length,
      };
    } catch (e) {
      return {
        'monthName': _getArabicMonthName(month),
        'totalAmount': 0.0,
        'totalCards': 0,
        'cardTypeStats': <Map<String, dynamic>>[],
        'debtsCount': 0,
      };
    }
  }

  // عرض نافذة تفاصيل الشهر
  void _showMonthlyDetailsDialog(
      BuildContext context, int month, int year) async {
    showDialog(
      context: context,
      builder: (context) => FutureBuilder<Map<String, dynamic>>(
        future: _getMonthlyDetails(month, year),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const AlertDialog(
              content: SizedBox(
                height: 100,
                child: Center(child: CircularProgressIndicator()),
              ),
            );
          }

          final data = snapshot.data ?? {};
          final monthName = data['monthName'] ?? 'غير محدد';
          final totalAmount = (data['totalAmount'] ?? 0.0) as double;
          final totalCards = (data['totalCards'] ?? 0) as int;
          final debtsCount = (data['debtsCount'] ?? 0) as int;
          final cardTypeStats =
              (data['cardTypeStats'] ?? []) as List<Map<String, dynamic>>;

          return AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
            title: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.blue.shade600, Colors.blue.shade700],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  const Icon(Icons.analytics, color: Colors.white, size: 24),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'تفاصيل مبيعات شهر $monthName',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            content: ConstrainedBox(
              constraints: const BoxConstraints(
                maxWidth: 400,
                maxHeight: 500,
              ),
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // الإحصائيات العامة
                    _buildMonthlyOverviewCards(
                        totalAmount, totalCards, debtsCount),

                    const SizedBox(height: 20),

                    // تفاصيل أنواع الكروت
                    if (cardTypeStats.isNotEmpty) ...[
                      const Align(
                        alignment: Alignment.centerRight,
                        child: Text(
                          'تفاصيل أنواع الكروت:',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                      const SizedBox(height: 12),

                      // قائمة أنواع الكروت
                      ...cardTypeStats.map(
                          (cardType) => _buildCardTypeDetailItem(cardType)),
                    ] else ...[
                      Container(
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade100,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.info_outline, color: Colors.grey),
                            SizedBox(width: 8),
                            Text(
                              'لا توجد مبيعات في هذا الشهر',
                              style: TextStyle(
                                color: Colors.grey,
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text(
                  'إغلاق',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  // بناء بطاقات الملخص الشهري
  Widget _buildMonthlyOverviewCards(
      double totalAmount, int totalCards, int debtsCount) {
    // تحديد قوة المبيعات بناءً على المبلغ
    String salesStrength;
    Color strengthColor;
    IconData strengthIcon;

    if (totalAmount >= 50000) {
      salesStrength = 'مبيعات قوية جداً';
      strengthColor = Colors.green.shade700;
      strengthIcon = Icons.trending_up;
    } else if (totalAmount >= 30000) {
      salesStrength = 'مبيعات قوية';
      strengthColor = Colors.green;
      strengthIcon = Icons.arrow_upward;
    } else if (totalAmount >= 15000) {
      salesStrength = 'مبيعات متوسطة';
      strengthColor = Colors.orange;
      strengthIcon = Icons.horizontal_rule;
    } else if (totalAmount >= 5000) {
      salesStrength = 'مبيعات ضعيفة';
      strengthColor = Colors.red.shade400;
      strengthIcon = Icons.arrow_downward;
    } else {
      salesStrength = 'مبيعات ضعيفة جداً';
      strengthColor = Colors.red.shade700;
      strengthIcon = Icons.trending_down;
    }

    return Column(
      children: [
        // البطاقة الأولى: إجمالي المبلغ
        SizedBox(
          width: double.infinity,
          child: _buildMonthlyStatCard(
            'إجمالي المبلغ',
            NumberFormatter.formatCurrency(totalAmount.toInt()),
            Icons.attach_money,
            Colors.green,
          ),
        ),
        const SizedBox(height: 12),

        // البطاقة الثانية: عدد الكروت
        SizedBox(
          width: double.infinity,
          child: _buildMonthlyStatCard(
            'عدد الكروت',
            NumberFormatter.formatNumber(totalCards),
            Icons.credit_card,
            Colors.blue,
          ),
        ),
        const SizedBox(height: 12),

        // البطاقة الثالثة: عدد المعاملات
        SizedBox(
          width: double.infinity,
          child: _buildMonthlyStatCard(
            'عدد المعاملات',
            NumberFormatter.formatNumber(debtsCount),
            Icons.receipt_long,
            Colors.orange,
          ),
        ),
        const SizedBox(height: 12),

        // البطاقة الرابعة: تقييم قوة المبيعات
        SizedBox(
          width: double.infinity,
          child: _buildMonthlyStatCard(
            'تقييم المبيعات',
            salesStrength,
            strengthIcon,
            strengthColor,
          ),
        ),
      ],
    );
  }

  // بطاقة إحصائية شهرية
  Widget _buildMonthlyStatCard(
      String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border:
            Border.all(color: Colors.teal.withValues(alpha: 0.3), width: 1.5),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // الأيقونة
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(icon, color: color, size: 28),
          ),
          const SizedBox(width: 16),

          // النصوص
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade700,
                    fontWeight: FontWeight.w600,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.right,
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.right,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // عنصر تفاصيل نوع الكارت
  Widget _buildCardTypeDetailItem(Map<String, dynamic> cardType) {
    final cardTypeName = cardType['cardType'] ?? 'غير محدد';
    final count = cardType['count'] ?? 0;
    final quantity = cardType['quantity'] ?? 0;
    final amount = (cardType['amount'] ?? 0.0) as double;
    final percentage = (cardType['percentage'] ?? 0.0) as double;

    final displayName = _getCardTypeDisplayName(cardTypeName);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border:
            Border.all(color: Colors.teal.withValues(alpha: 0.3), width: 1.5),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // الصف الأول: الأيقونة واسم الكارت والنسبة المئوية
          Row(
            children: [
              // أيقونة نوع الكارت
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: Colors.teal.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(
                  Icons.credit_card,
                  color: Colors.teal,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),

              // اسم نوع الكارت
              Expanded(
                child: Text(
                  displayName,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.right,
                ),
              ),

              const SizedBox(width: 12),

              // النسبة المئوية
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.teal.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  '${percentage.toStringAsFixed(1)}%',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.teal,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // الصف الثاني: التفاصيل والمبلغ
          Row(
            children: [
              // التفاصيل
              Expanded(
                child: Wrap(
                  spacing: 8,
                  runSpacing: 4,
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.blue.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        '$quantity كارت',
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.blue,
                          fontWeight: FontWeight.w600,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.orange.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        '$count معاملة',
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.orange,
                          fontWeight: FontWeight.w600,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(width: 12),

              // المبلغ
              Flexible(
                child: Text(
                  NumberFormatter.formatCurrency(amount.toInt()),
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.teal,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.left,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
